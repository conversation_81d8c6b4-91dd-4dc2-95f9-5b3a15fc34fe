'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { StarLoader } from '@/components/ui/star-loader'

interface LibraryBook {
  id: string
  title: string
  cover_image_url: string | null
  author_name: string
  access_type: 'purchased' | 'free' | 'preview'
  added_at: string
  last_accessed_at: string
  total_chapters: number
  total_words: number
  reading_time_minutes: number
  users: {
    name: string
    avatar: string | null
  }
}

export default function LibraryPage() {
  const [books, setBooks] = useState<LibraryBook[]>([])
  const [loading, setLoading] = useState(true)
  const [user, setUser] = useState<any>(null)
  const [readingBookId, setReadingBookId] = useState<string | null>(null)
  const [deletingBookId, setDeletingBookId] = useState<string | null>(null)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [bookToDelete, setBookToDelete] = useState<LibraryBook | null>(null)
  const router = useRouter()
  const supabase = createSupabaseClient()

  useEffect(() => {
    checkUser()
  }, [])

  const checkUser = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      router.push('/login')
      return
    }
    setUser(user)
    fetchLibraryBooks(user.id)
  }

  const fetchLibraryBooks = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('user_library')
        .select(`
          access_type,
          added_at,
          last_accessed_at,
          projects!inner(
            id,
            title,
            cover_image_url,
            author_name,
            total_chapters,
            total_words,
            reading_time_minutes,
            users!inner(name, avatar)
          )
        `)
        .eq('user_id', userId)
        .order('added_at', { ascending: false })

      if (error) {
        console.error('Error fetching library:', error)
        return
      }

      const libraryBooks = data?.map((item: any) => ({
        id: item.projects.id,
        title: item.projects.title,
        cover_image_url: item.projects.cover_image_url,
        author_name: item.projects.author_name,
        access_type: item.access_type,
        added_at: item.added_at,
        last_accessed_at: item.last_accessed_at,
        total_chapters: item.projects.total_chapters,
        total_words: item.projects.total_words,
        reading_time_minutes: item.projects.reading_time_minutes,
        users: item.projects.users
      })) || []

      setBooks(libraryBooks)
    } catch (error) {
      console.error('Error fetching library books:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatReadingTime = (minutes: number) => {
    if (minutes < 60) return `${minutes}m`
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`
  }

  const formatPages = (words: number) => {
    return Math.ceil((words / 1000) * 4)
  }

  const handleReadClick = (bookId: string) => {
    setReadingBookId(bookId)
    // All books now use the same EPUB reader (PDFs are converted to EPUB)
    router.push(`/books/${bookId}/read`)
  }

  const handleDeleteClick = (book: LibraryBook) => {
    setBookToDelete(book)
    setShowDeleteModal(true)
  }

  const handleDeleteConfirm = async () => {
    if (!bookToDelete || !user) return

    setDeletingBookId(bookToDelete.id)
    try {
      const { error } = await supabase
        .from('user_library')
        .delete()
        .eq('user_id', user.id)
        .eq('project_id', bookToDelete.id)

      if (error) {
        console.error('Error deleting book from library:', error)
        alert('Failed to remove book from library. Please try again.')
        return
      }

      // Remove book from local state
      setBooks(books.filter(book => book.id !== bookToDelete.id))
      setShowDeleteModal(false)
      setBookToDelete(null)
    } catch (error) {
      console.error('Error deleting book:', error)
      alert('Failed to remove book from library. Please try again.')
    } finally {
      setDeletingBookId(null)
    }
  }

  const handleDeleteCancel = () => {
    setShowDeleteModal(false)
    setBookToDelete(null)
  }

  // Reset loading state when component unmounts or after timeout
  useEffect(() => {
    if (readingBookId) {
      const timeout = setTimeout(() => {
        setReadingBookId(null)
      }, 3000) // Reset after 3 seconds as fallback

      return () => clearTimeout(timeout)
    }
  }, [readingBookId])

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50">
        <div className="max-w-7xl mx-auto px-3 sm:px-4 py-4 sm:py-8">
          {/* Header */}
          <div className="mb-6 sm:mb-8 flex flex-col items-center justify-center w-full">
            <h1 className="text-2xl sm:text-3xl font-serif text-gray-900 mb-2 text-center">My Library</h1>
            <p className="text-sm sm:text-base text-gray-600 text-center">Your collection of purchased and free books</p>
          </div>

          {/* Page Flip Loading Animation */}
          <div className="text-center py-8 sm:py-12">
            {/* Main Animation Container */}
            <div className="relative h-48 sm:h-64 mb-8 flex items-center justify-center">
              {/* Open Book with Flipping Pages */}
              <div className="relative">
                {/* Book Base */}
                <div className="w-32 h-20 sm:w-40 sm:h-24 bg-gradient-to-br from-amber-100 to-amber-200 rounded-lg shadow-lg border-2 border-amber-300 relative">
                  {/* Book Spine */}
                  <div className="absolute left-0 top-0 bottom-0 w-2 bg-gradient-to-b from-amber-400 to-amber-600 rounded-l-lg"></div>

                  {/* Left Page (Static) */}
                  <div className="absolute left-2 top-2 bottom-2 w-12 sm:w-16 bg-white rounded shadow-sm border border-gray-200">
                    <div className="p-1 sm:p-2 space-y-1">
                      <div className="h-1 bg-gray-300 rounded w-full"></div>
                      <div className="h-1 bg-gray-300 rounded w-3/4"></div>
                      <div className="h-1 bg-gray-300 rounded w-full"></div>
                      <div className="h-1 bg-gray-300 rounded w-1/2"></div>
                    </div>
                  </div>

                  {/* Right Pages (Flipping) */}
                  <div className="absolute right-2 top-2 bottom-2 w-12 sm:w-16">
                    {/* Page 1 */}
                    <div
                      className="absolute inset-0 bg-white rounded shadow-sm border border-gray-200 origin-left"
                      style={{
                        animation: 'pageFlip1 2s ease-in-out infinite',
                        transformStyle: 'preserve-3d'
                      }}
                    >
                      <div className="p-1 sm:p-2 space-y-1">
                        <div className="h-1 bg-purple-300 rounded w-full"></div>
                        <div className="h-1 bg-purple-300 rounded w-2/3"></div>
                        <div className="h-1 bg-purple-300 rounded w-full"></div>
                        <div className="h-1 bg-purple-300 rounded w-3/4"></div>
                      </div>
                    </div>

                    {/* Page 2 */}
                    <div
                      className="absolute inset-0 bg-white rounded shadow-sm border border-gray-200 origin-left"
                      style={{
                        animation: 'pageFlip2 2s ease-in-out infinite',
                        animationDelay: '0.3s',
                        transformStyle: 'preserve-3d'
                      }}
                    >
                      <div className="p-1 sm:p-2 space-y-1">
                        <div className="h-1 bg-blue-300 rounded w-full"></div>
                        <div className="h-1 bg-blue-300 rounded w-1/2"></div>
                        <div className="h-1 bg-blue-300 rounded w-full"></div>
                        <div className="h-1 bg-blue-300 rounded w-4/5"></div>
                      </div>
                    </div>

                    {/* Page 3 */}
                    <div
                      className="absolute inset-0 bg-white rounded shadow-sm border border-gray-200 origin-left"
                      style={{
                        animation: 'pageFlip3 2s ease-in-out infinite',
                        animationDelay: '0.6s',
                        transformStyle: 'preserve-3d'
                      }}
                    >
                      <div className="p-1 sm:p-2 space-y-1">
                        <div className="h-1 bg-green-300 rounded w-full"></div>
                        <div className="h-1 bg-green-300 rounded w-3/5"></div>
                        <div className="h-1 bg-green-300 rounded w-full"></div>
                        <div className="h-1 bg-green-300 rounded w-2/3"></div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Floating Page Effect */}
                <div className="absolute -top-4 -right-4 w-8 h-10 sm:w-10 sm:h-12 bg-white rounded shadow-lg border border-gray-200 opacity-80 animate-float">
                  <div className="p-1 space-y-1">
                    <div className="h-0.5 bg-gray-300 rounded w-full"></div>
                    <div className="h-0.5 bg-gray-300 rounded w-2/3"></div>
                    <div className="h-0.5 bg-gray-300 rounded w-full"></div>
                  </div>
                </div>

                {/* Reading Sparkles */}
                {[...Array(6)].map((_, i) => (
                  <div
                    key={`sparkle-${i}`}
                    className="absolute w-1 h-1 bg-yellow-400 rounded-full opacity-70"
                    style={{
                      animation: `sparkle 1.5s ease-in-out infinite`,
                      animationDelay: `${i * 0.25}s`,
                      left: `${-20 + (i * 15)}px`,
                      top: `${-10 + (i % 3) * 15}px`,
                    }}
                  />
                ))}
              </div>
            </div>

            {/* Loading Text */}
            <div className="space-y-3">
              <h2 className="text-xl sm:text-2xl font-semibold text-gray-900 mb-2">
                <span className="inline-block animate-pulse">📖</span>
                {" "}Reading Your Library
                <span className="inline-block animate-pulse">📚</span>
              </h2>
              <p className="text-gray-600 text-sm sm:text-base">Flipping through your collection...</p>

              {/* Page Turn Indicator */}
              <div className="flex justify-center items-center space-x-2 mt-4">
                <span className="text-sm text-gray-500">Page</span>
                {[...Array(3)].map((_, i) => (
                  <div
                    key={i}
                    className="w-2 h-2 bg-amber-500 rounded-full animate-bounce"
                    style={{ animationDelay: `${i * 0.3}s` }}
                  />
                ))}
                <span className="text-sm text-gray-500">turning...</span>
              </div>
            </div>

            {/* Mobile-Optimized Book Shelf Preview */}
            <div className="mt-8 sm:mt-12">
              <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3 sm:gap-4 max-w-4xl mx-auto">
                {[1, 2, 3, 4, 5, 6].map((i) => (
                  <div
                    key={i}
                    className="bg-white/70 backdrop-blur-sm rounded-lg shadow-sm border border-white/50 p-3 sm:p-4 animate-pulse"
                    style={{ animationDelay: `${i * 0.1}s` }}
                  >
                    <div className="aspect-[3/4] bg-gradient-to-br from-gray-200 to-gray-300 rounded mb-2 sm:mb-3 relative overflow-hidden">
                      {/* Shimmer Effect */}
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-shimmer"></div>
                    </div>
                    <div className="space-y-1 sm:space-y-2">
                      <div className="h-3 sm:h-4 bg-gray-200 rounded"></div>
                      <div className="h-2 sm:h-3 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-6 sm:h-8 bg-purple-200 rounded w-16 sm:w-20 mt-2"></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Custom CSS Animations */}
        <style jsx>{`
          @keyframes pageFlip1 {
            0% { transform: rotateY(0deg); z-index: 3; }
            25% { transform: rotateY(-90deg); z-index: 3; }
            50% { transform: rotateY(-180deg); z-index: 1; }
            75% { transform: rotateY(-180deg); z-index: 1; }
            100% { transform: rotateY(0deg); z-index: 3; }
          }
          @keyframes pageFlip2 {
            0% { transform: rotateY(0deg); z-index: 2; }
            25% { transform: rotateY(0deg); z-index: 2; }
            50% { transform: rotateY(-90deg); z-index: 2; }
            75% { transform: rotateY(-180deg); z-index: 1; }
            100% { transform: rotateY(0deg); z-index: 2; }
          }
          @keyframes pageFlip3 {
            0% { transform: rotateY(0deg); z-index: 1; }
            50% { transform: rotateY(0deg); z-index: 1; }
            75% { transform: rotateY(-90deg); z-index: 1; }
            100% { transform: rotateY(-180deg); z-index: 1; }
          }
          @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(-5deg); }
            50% { transform: translateY(-10px) rotate(5deg); }
          }
          @keyframes sparkle {
            0%, 100% { opacity: 0; transform: scale(0) rotate(0deg); }
            50% { opacity: 1; transform: scale(1) rotate(180deg); }
          }
          @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
          }
        `}</style>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-3 sm:px-4 py-4 sm:py-8">
        {/* Header */}
        <div className="mb-6 sm:mb-8 flex flex-col items-center justify-center w-full">
          <h1 className="text-2xl sm:text-3xl font-serif text-gray-900 mb-2 text-center">My Library</h1>
          <p className="text-sm sm:text-base text-gray-600 text-center">Your collection of purchased and free books</p>
        </div>

        {books.length === 0 ? (
          <div className="text-center py-16">
            <div className="text-6xl mb-4">📚</div>
            <h2 className="text-2xl font-semibold text-gray-900 mb-2">Your library is empty</h2>
            <p className="text-gray-600 mb-6">Start building your collection by purchasing or downloading free books</p>
            <Link href="/books">
              <Button size="lg">Browse Books</Button>
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3 sm:gap-4">
            {books.map((book) => (
              <div key={book.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-all duration-200 hover:scale-[1.02]">
                {/* Book Cover */}
                <div className="aspect-[3/4] relative bg-gradient-to-br from-gray-50 to-gray-100">
                  {book.cover_image_url ? (
                    <Image
                      src={book.cover_image_url}
                      alt={book.title}
                      fill
                      className="object-contain p-1"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-gray-400">
                      <div className="text-3xl sm:text-4xl">📖</div>
                    </div>
                  )}

                  {/* Access Type Badge */}
                  <div className="absolute top-1 right-1">
                    <span className={`px-1.5 py-0.5 text-xs font-medium rounded-full ${
                      book.access_type === 'purchased'
                        ? 'bg-green-100 text-green-800'
                        : book.access_type === 'free'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {book.access_type === 'purchased' ? 'Owned' :
                       book.access_type === 'free' ? 'Free' : 'Preview'}
                    </span>
                  </div>
                </div>

                {/* Book Info */}
                <div className="p-2 sm:p-3">
                  <h3 className="font-semibold text-gray-900 mb-1 line-clamp-2 text-sm sm:text-base">{book.title}</h3>
                  <p className="text-xs sm:text-sm text-gray-600 mb-2">by {book.users.name}</p>

                  {/* Book Stats */}
                  <div className="text-xs text-gray-500 mb-2 space-y-0.5">
                    <div className="flex justify-between">
                      <span>{book.total_chapters} ch</span>
                      <span>~{formatPages(book.total_words)}p</span>
                    </div>
                    <div className="flex justify-between">
                      <span>{formatReadingTime(book.reading_time_minutes)}</span>
                      <span className="hidden sm:inline">Added {new Date(book.added_at).toLocaleDateString()}</span>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="space-y-1.5">
                    <Button
                      className="w-full text-xs sm:text-sm h-8 sm:h-9 border-0 font-medium"
                      size="sm"
                      onClick={() => handleReadClick(book.id)}
                      disabled={readingBookId === book.id}
                    >
                      {readingBookId === book.id ? (
                        <StarLoader size="sm" />
                      ) : (
                        <>{book.access_type === 'preview' ? 'PREVIEW' : 'READ'}</>
                      )}
                    </Button>
                    <div className="flex gap-1">
                      <Link href={`/books/${book.id}`} className="flex-1">
                        <Button variant="outline" className="w-full text-xs sm:text-sm h-7 sm:h-8" size="sm">
                          📋 Details
                        </Button>
                      </Link>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteClick(book)}
                        disabled={deletingBookId === book.id}
                        className="h-7 sm:h-8 px-2 text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200"
                      >
                        {deletingBookId === book.id ? (
                          <StarLoader size="sm" />
                        ) : (
                          '🗑️'
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteModal && bookToDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <div className="text-center">
              <div className="text-4xl mb-4">🗑️</div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Remove from Library?
              </h3>
              <p className="text-gray-600 mb-6">
                Are you sure you want to remove "<strong>{bookToDelete.title}</strong>" from your library?
                {bookToDelete.access_type === 'purchased' && (
                  <span className="block mt-2 text-sm text-amber-600">
                    ⚠️ You purchased this book. You can re-download it anytime from the book's page.
                  </span>
                )}
              </p>

              <div className="flex gap-3 justify-center">
                <Button
                  variant="outline"
                  onClick={handleDeleteCancel}
                  disabled={deletingBookId === bookToDelete.id}
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleDeleteConfirm}
                  disabled={deletingBookId === bookToDelete.id}
                  isLoading={deletingBookId === bookToDelete.id}
                >
                  {deletingBookId === bookToDelete.id ? 'Removing...' : 'Remove from Library'}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
